# Variables
PROJECT_NAME = robot-world

.PHONY: all compile test reference-server own-server clean package release tag stop bump dev full_run_ref full_run_own bump-major bump-minor bump-patch release-version

all: compile test

compile:
	@bash ./scripts/compile.sh

test: reference-server
	@bash ./scripts/test.sh

reference-server:
	@bash ./scripts/reference-server.sh

own-server:
	@bash ./scripts/own-server.sh

package:
	@mvn package

release: clean release-version reference-server package tag

release-version:
	@echo "Removing -SNAPSHOT from version..."
	@version=$$(grep '<version>' pom.xml | head -1 | sed -E 's/.*<version>([^<]+)-SNAPSHOT<\/version>.*/\1/'); \
	sed -i 's|<version>'"$$version"'-SNAPSHOT</version>|<version>'"$$version"'</version>|' pom.xml
	@echo "Done"


tag:
	@bash ./scripts/tag.sh

bump:
	@bash ./scripts/bump_version.sh

bump-minor:
	@bash ./scripts/bump_version.sh MINOR

bump-major:
	@bash ./scripts/bump_version.sh MAJOR

bump-patch:
	@bash ./scripts/bump_version.sh PATCH

clean:
	@bash ./scripts/clean.sh

stop:
	@bash ./scripts/stop.sh

dev:
	mvn clean compile test

full_run_ref:
	@bash ./scripts/full_run_reference_server.sh

full_run_own:
	@bash ./scripts/full_run_own_server.sh
