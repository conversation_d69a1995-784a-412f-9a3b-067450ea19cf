package za.co.wethinkcode.robots.server;
import za.co.wethinkcode.flow.Recorder;
import za.co.wethinkcode.robots.handlers.ClientHandler;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Scanner;

/**
 * Main server class that accepts client connections and provides an admin console for server control.
 * Supports real-time robot monitoring, world state inspection, and graceful shutdown.
 */
@Command(name = "robot-server", mixinStandardHelpOptions = true, version = "1.0.0",
         description = "Robot World Server - Manages robot connections and world state")
public class Server implements Runnable {
    private static volatile boolean isRunning = true;
    private static ServerSocket serverSocket;

    @Option(names = {"-p", "--port"}, description = "Server port (0-9999, default: ${DEFAULT-VALUE})", defaultValue = "5000")
    private int port;

    @Option(names = {"-s", "--size"}, description = "World size (1-9999, creates size x size world, default: ${DEFAULT-VALUE})", defaultValue = "1")
    private int worldSize;

    @Option(names = {"-o", "--obstacles"}, description = "Obstacle positions in format x,y (default: none)", split = ",")
    private String[] obstaclePositions;

    public static void main(String[] args) {
        int exitCode = new CommandLine(new Server()).execute(args);
        System.exit(exitCode);
    }

    @Override
    public void run() {
        // Validate and sanitize arguments
        int validatedPort = validatePort(port);
        int validatedWorldSize = validateWorldSize(worldSize);

        // Create world with validated size
        World world = createWorldWithSize(validatedWorldSize);

        // Add obstacles if specified
        if (obstaclePositions != null && obstaclePositions.length >= 2) {
            addObstacleToWorld(world, obstaclePositions);
        }

        try {
            serverSocket = new ServerSocket(validatedPort);
            System.out.println("Server started on port " + validatedPort + ". Waiting for clients...");

            // launch admin console thread
            startAdminConsole(world);

            while (isRunning) {
                Socket clientSocket = serverSocket.accept();
                System.out.println("New client connected: " + clientSocket.getRemoteSocketAddress());
                new Thread(new ClientHandler(clientSocket, world)).start(); // start new thread to handle multiple clients
            }

        } catch (IOException e) {
            if (!isRunning) {
                System.out.println("Sever shutdown.");
            } else {
                System.out.println("Got an error: " + e);
            }
        }
    }

    /**
     * Validates port number is within range 0-9999, returns default if invalid
     */
    private int validatePort(int port) {
        if (port < 0 || port > 9999) {
            System.out.println("Warning: Port " + port + " is out of range (0-9999). Using default port 5000.");
            return 5000;
        }
        return port;
    }

    /**
     * Validates world size is within range 1-9999, returns default if invalid
     */
    private int validateWorldSize(int size) {
        if (size < 1 || size > 9999) {
            System.out.println("Warning: World size " + size + " is out of range (1-9999). Using default size 1.");
            return 1;
        }
        return size;
    }

    /**
     * Creates a world with the specified size, or uses the default singleton if size is 1
     */
    private World createWorldWithSize(int size) {
        if (size == 1) {
            // Use the default singleton world which loads from config.properties
            return World.getInstance();
        } else {
            // Create a new world with custom dimensions
            System.out.println("Creating custom world...");
            World world = new World(size, size);
            System.out.println("World successfully created with dimensions: " + world.getWidth() + " x " + world.getHeight());
            world.displayWorld();
            return world;
        }
    }

    /**
     * Adds an obstacle to the world at the specified position
     */
    private void addObstacleToWorld(World world, String[] positions) {
        try {
            if (positions.length >= 2) {
                int x = Integer.parseInt(positions[0].trim());
                int y = Integer.parseInt(positions[1].trim());

                // Validate obstacle coordinates are reasonable (within world bounds or close)
                int worldHalfWidth = world.getHalfWidth();
                int worldHalfHeight = world.getHalfHeight();

                if (Math.abs(x) > worldHalfWidth || Math.abs(y) > worldHalfHeight) {
                    System.out.println("Warning: Obstacle position [" + x + "," + y + "] is outside world bounds. Skipping obstacle placement.");
                    return;
                }

                // Create a 1x1 obstacle at the specified position
                Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, x, y, 1, 1);
                boolean added = world.addObstacle(obstacle);

                if (added) {
                    System.out.println("Added obstacle at position [" + x + "," + y + "]");
                } else {
                    System.out.println("Warning: Could not add obstacle at position [" + x + "," + y + "] - position may be invalid or occupied");
                }
            } else {
                System.out.println("Warning: Invalid obstacle format. Expected: x,y (e.g., 10,5)");
            }
        } catch (NumberFormatException e) {
            System.out.println("Warning: Invalid obstacle position format. Expected: x,y with integer coordinates (e.g., 10,5)");
        }
    }

    private static void startAdminConsole(World world) {
        new Thread(() -> {
            try (Scanner scanner = new Scanner(System.in)) {
                while (isRunning) {
                    System.out.println("Valid Commands: 'quit', 'robots', 'dump', 'display'");
                    System.out.print("[Admin]: ");
                    String input = scanner.nextLine().trim().toLowerCase();
                    switch (input) {
                        case "quit":
                            System.out.println("Shutting down server...");
                            shutdown();
                            break;
                        case "robots":
                            System.out.println(world.getAllRobotsInfo());
                            break;
                        case "dump":
                            System.out.println(world.getFullWorldState());
                            break;
                        case "display":
                            world.displayWorld();
                            break;
                        default:
                            System.out.println("Unknown admin command.");
                    }
                }
            }
        }, "AdminConsole").start();
    }


    public static void shutdown() {
        isRunning = false;
        try {
            serverSocket.close();
        } catch (IOException e) {
            System.out.println("Got an error when shutting down: " + e);
        }
    }
    static {
        new Recorder().logRun();
    }
}

